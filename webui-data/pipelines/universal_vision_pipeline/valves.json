{"pipelines": ["*"], "priority": 0, "gemini_api_key": "AIzaSyDdXCS0Cw58rfurmaKW061IdkOy1UHK51U", "gemini_model": "gemini-2.5-flash-lite", "enable_vision_processing": true, "vision_prompt_template": "Analyze this image in detail. Describe what you see, including objects, people, text, colors, composition, and any other relevant details. Be comprehensive and accurate.", "max_image_size": 1024, "image_quality": 85, "enable_document_processing": false, "document_prompt_template": "Summarize the key points of this document. Identify the main topics, conclusions, and any action items mentioned.", "supported_document_mime_types": ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessing.document", "text/plain", "text/markdown", "text/csv", "application/msword"], "non_vision_models": ["gpt-3.5-turbo", "gpt-4", "claude-3-haiku", "claude-3-sonnet", "llama", "mistral", "gemma", "qwen", "deepseek", "phi", "codellama", "vicuna", "alpaca", "openchat", "starling", "GLM"], "inject_analysis_as_text": true, "analysis_response_prefix": "[Attachment Analysis]", "enable_debug_logging": true, "log_file_processing": false}